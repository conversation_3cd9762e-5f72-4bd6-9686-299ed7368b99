"""
FMDLES Client Implementation

This module implements the FMDLES client that integrates with the pyfmto framework.
The client performs federated multi-task optimization using deep learning enhanced search.
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import copy
from typing import Dict, List, Optional
import logging
from tqdm import tqdm

from pyfmto.framework import Client, record_runtime, ClientPackage, ServerPackage
from .fmdles_utils import (
    DLESNetwork, 
    UMDAc, 
    DLES, 
    FMDLESActions,
    fitness_mse_loss,
    normalize_objectives,
    get_unique_indices
)

logger = logging.getLogger(__name__)


class FMDLESClient(Client):
    """
    max_fe: 110
    exploration_ratio: 0.7
    population_size: 10
    exploration_iterations: 20
    learning_rate: 0.3
    epochs: 3
    iid: 1
    np_per_dim: 2
    """

    def __init__(self, problem, **kwargs):
        super().__init__(problem)
        kwargs = self.update_kwargs(kwargs)

        # Algorithm parameters
        self.max_fe = kwargs.get('max_fe', 110)
        self.exploration_ratio = kwargs.get('exploration_ratio', 0.7)
        # 修复：使用fe_init作为种群大小，与其他算法保持一致
        self.population_size = kwargs.get('population_size', self.problem.fe_init)
        self.exploration_iterations = kwargs.get('exploration_iterations', 20)
        self.learning_rate = kwargs.get('learning_rate', 0.3)
        self.epochs = kwargs.get('epochs', 3)
        self.iid = bool(kwargs.get('iid', 1))  # Convert to boolean
        self.np_per_dim = kwargs.get('np_per_dim', 2)

        # Evaluation allocation
        self.exploration_evaluations = int(self.max_fe * self.exploration_ratio)
        self.exploitation_evaluations = self.max_fe - self.exploration_evaluations

        # Algorithm state
        self.phase = 'exploration'  # exploration, training, exploitation
        self.local_dataset = []
        self.trajectory_data = {
            'best_fitness_sequence': [],
            'improvement_rates': [],
            'evaluation_points': []
        }

        # Models
        self.local_model = DLESNetwork(self.dim)
        self.global_model = None
        self.local_dles = None

        # Data partition bounds for Non-IID
        self.partition_bounds = self._compute_partition_bounds()

        # Initialize DLES algorithm
        self._initialize_dles()

    def _compute_partition_bounds(self):
        """Compute data partition bounds for Non-IID setting"""
        if self.iid:
            return None
        else:
            # Non-IID: partition search space based on client ID
            bounds = []
            for d in range(self.dim):
                partition_size = 200.0 / self.np_per_dim  # Total range 200 (-100 to 100)
                partition_id = self.id % self.np_per_dim
                
                lower = -100.0 + partition_id * partition_size
                upper = -100.0 + (partition_id + 1) * partition_size
                bounds.append([lower, upper])
            
            return np.array(bounds)

    def _initialize_dles(self):
        """Initialize local DLES algorithm"""
        # Create a wrapper function for the objective function
        def objective_wrapper(x):
            # Ensure x is 2D for framework evaluation
            if x.ndim == 1:
                x = x.reshape(1, -1)
            result = self.problem.evaluate(x)
            return result.flatten()[0] if result.size == 1 else result.flatten()

        self.local_dles = DLES(
            objective_function=objective_wrapper,
            dimension=self.dim,
            population_size=self.population_size,
            max_evaluations=self.exploration_evaluations,
            exploration_iterations=self.exploration_iterations,
            learning_rate=self.learning_rate,
            epochs=self.epochs
        )

        # Override evaluate method to track trajectory
        original_evaluate = self.local_dles.evaluate
        
        def evaluate_with_tracking(individuals):
            fitness_values = original_evaluate(individuals)
            self._update_trajectory_data()
            return fitness_values
        
        self.local_dles.evaluate = evaluate_with_tracking

    def _update_trajectory_data(self):
        """Update optimization trajectory data"""
        if self.local_dles.best_fitness != float('inf'):
            current_best = self.local_dles.best_fitness
            self.trajectory_data['best_fitness_sequence'].append(current_best)
            self.trajectory_data['evaluation_points'].append(self.local_dles.evaluations)

            # Calculate improvement rate
            if len(self.trajectory_data['best_fitness_sequence']) > 1:
                prev_best = self.trajectory_data['best_fitness_sequence'][-2]
                if prev_best != 0:
                    improvement_rate = (prev_best - current_best) / abs(prev_best)
                else:
                    improvement_rate = 0.0
                self.trajectory_data['improvement_rates'].append(improvement_rate)
            else:
                self.trajectory_data['improvement_rates'].append(0.0)

    def start(self):
        """Override start method to fix progress bar display"""
        try:
            logger.info(f"{self.name} started")
            self._Client__register_id()
            self._Client__logging_params()

            # Fixed progress bar with better configuration
            pbar = tqdm(
                total=self.fe_max,
                desc=f"Client {self.id}",
                unit="eval",
                ncols=80,
                position=None,  # Let tqdm auto-assign position
                leave=True,     # Keep progress bar after completion
                dynamic_ncols=True
            )

            prev_fe_used = 0
            while self.problem.fe_available > 0:
                self.optimize()
                current_fe_used = self.fe_max - self.problem.fe_available
                fe_increment = current_fe_used - prev_fe_used
                if fe_increment > 0:
                    pbar.update(fe_increment)
                    prev_fe_used = current_fe_used
                self._Client__logging_round_info(only_latest=True)

            pbar.close()
            self._Client__logging_round_info(only_latest=False)
            self.send_quit()
            logger.info(f"{self.name} exit with available FE = {self.problem.fe_available}")
        except Exception:
            self.send_quit()
            if self.id == 1:
                print(f"Traceback of {self.name}")
                import traceback
                traceback.print_exc()
                logger.error(traceback.format_exc())
            logger.info(f"{self.name} exit with available FE = {self.problem.fe_available}")
            exit(-1)
        return self.id, self.solutions

    def optimize(self):
        """Main optimization loop"""
        if self.phase == 'exploration':
            self._exploration_phase()
        elif self.phase == 'training':
            self._training_phase()
        elif self.phase == 'exploitation':
            self._exploitation_phase()

    @record_runtime("Exploration Phase")
    def _exploration_phase(self):
        """Phase 1: Local exploration using DLES"""
        logger.info(f"{self.name} starting exploration phase")

        # Perform local exploration
        self.local_dles.exploration_sampling()
        
        # Store local dataset
        self.local_dataset = self.local_dles.dataset.copy()

        # Push exploration results to server
        exploration_data = {
            'client_id': self.id,
            'best_fitness': self.local_dles.best_fitness,
            'dataset_size': len(self.local_dataset),
            'evaluations_used': self.local_dles.evaluations,
            'trajectory_data': self.trajectory_data.copy(),
            'partition_info': 'IID' if self.iid else f'Non-IID-{self.np_per_dim}'
        }

        pkg = ClientPackage(self.id, FMDLESActions.PUSH_EXPLORATION_RESULTS, exploration_data)
        self.request_server(pkg)

        # Pull exploration statistics
        pkg = ClientPackage(self.id, FMDLESActions.PULL_EXPLORATION_STATS, None)
        response = self.request_server(pkg, repeat=100)

        if response and response.data:
            logger.info(f"{self.name} received exploration stats: {response.data}")

        self.phase = 'training'

    @record_runtime("Training Phase")
    def _training_phase(self):
        """Phase 2: Local model training and federated aggregation"""
        logger.info(f"{self.name} starting training phase")

        # Train local model
        local_model_data = self._train_local_model()

        if local_model_data:
            # Push local model to server
            pkg = ClientPackage(self.id, FMDLESActions.PUSH_LOCAL_MODEL, local_model_data)
            self.request_server(pkg)

            # Pull aggregated model from server
            pkg = ClientPackage(self.id, FMDLESActions.PULL_AGGREGATED_MODEL, None)
            response = self.request_server(pkg, repeat=100)

            if response and response.data:
                aggregated_model = response.data.get('aggregated_model')
                if aggregated_model:
                    self.global_model = DLESNetwork(self.dim)
                    self.global_model.load_state_dict(aggregated_model)
                    logger.info(f"{self.name} received aggregated model")

        self.phase = 'exploitation'

    def _train_local_model(self) -> Optional[Dict]:
        """Train local model on collected data"""
        if len(self.local_dataset) < 2:
            logger.warning(f"{self.name} insufficient data for training")
            return None

        # Prepare training data
        individuals = np.array([item[0] for item in self.local_dataset])
        fitness_values = np.array([item[1] for item in self.local_dataset])

        X_fitness = torch.FloatTensor(fitness_values).reshape(-1, 1)
        y_individuals = torch.FloatTensor(individuals)

        # Train local model
        optimizer = torch.optim.Adam(self.local_model.parameters(), lr=self.learning_rate)

        for epoch in range(self.epochs):
            optimizer.zero_grad()
            predictions = self.local_model(X_fitness)

            # Use MSE loss (simplified version)
            loss = torch.mean((predictions - y_individuals)**2)
            loss.backward()
            optimizer.step()

        logger.info(f"{self.name} local model training completed, loss: {loss.item():.6f}")

        return {
            'client_id': self.id,
            'model_state_dict': copy.deepcopy(self.local_model.state_dict()),
            'dataset_size': len(self.local_dataset)
        }

    @record_runtime("Exploitation Phase")
    def _exploitation_phase(self):
        """Phase 3: Federated exploitation using global model"""
        logger.info(f"{self.name} starting exploitation phase")

        # Reset evaluation counter for exploitation
        initial_evaluations = self.local_dles.evaluations
        self.local_dles.max_evaluations = initial_evaluations + self.exploitation_evaluations

        # Use global model if available, otherwise use local model
        model_to_use = self.global_model if self.global_model else self.local_model

        # Predict optimal individual using model
        model_to_use.eval()
        with torch.no_grad():
            fitness_tensor = torch.FloatTensor([self.local_dles.best_fitness]).reshape(-1, 1)
            network_solution = model_to_use(fitness_tensor).numpy().flatten()

        # Evaluate network prediction
        if self.local_dles.evaluations < self.local_dles.max_evaluations:
            network_fitness_array = self.local_dles.evaluate(network_solution)
            if len(network_fitness_array) > 0:
                network_fitness = network_fitness_array[0]
                if network_fitness < self.local_dles.best_fitness:
                    self.local_dles.best_fitness = network_fitness
                    self.local_dles.best_individual = network_solution.copy()

        # Simplified local search (can be extended with CMA-ES and univariate sampling)
        self._local_search()

        # Push final results
        final_data = {
            'client_id': self.id,
            'final_best_fitness': self.local_dles.best_fitness,
            'final_evaluations': self.local_dles.evaluations,
            'improvement_from_federation': self.global_model is not None
        }

        pkg = ClientPackage(self.id, FMDLESActions.PUSH_FINAL_RESULTS, final_data)
        self.request_server(pkg)

        # Update solutions for framework
        if self.local_dles.best_individual is not None:
            best_x = self.local_dles.best_individual.reshape(1, -1)
            best_y = np.array([[self.local_dles.best_fitness]])
            self.solutions.append(best_x, best_y)

        logger.info(f"{self.name} exploitation completed, best fitness: {self.local_dles.best_fitness:.6e}")

    def _local_search(self):
        """Simplified local search around current best solution"""
        if self.local_dles.evaluations >= self.local_dles.max_evaluations:
            return

        current_solution = self.local_dles.best_individual.copy()
        current_fitness = self.local_dles.best_fitness

        # Simple random search around current best
        for _ in range(min(10, self.local_dles.max_evaluations - self.local_dles.evaluations)):
            if self.local_dles.evaluations >= self.local_dles.max_evaluations:
                break

            # Generate candidate solution
            candidate = current_solution + np.random.normal(0, 0.1, self.dim)
            
            # Clip to bounds
            candidate = np.clip(candidate, self.x_lb, self.x_ub)

            # Evaluate candidate
            candidate_fitness_array = self.local_dles.evaluate(candidate)
            if len(candidate_fitness_array) > 0:
                candidate_fitness = candidate_fitness_array[0]
                if candidate_fitness < current_fitness:
                    current_solution = candidate.copy()
                    current_fitness = candidate_fitness
                    self.local_dles.best_individual = current_solution
                    self.local_dles.best_fitness = current_fitness

    def check_pkg(self, x: ServerPackage) -> bool:
        """Check if server package is acceptable"""
        if x is None:
            return False
        return x.data is not None
